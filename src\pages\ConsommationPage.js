import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const ConsommationPage = ({ user, onBack, onShowResults, selectedClientFromList, onClearSelectedClient, onShowClientList }) => {
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState(''); // Client sélectionné
  const [filteredContracts, setFilteredContracts] = useState([]); // Contrats du client sélectionné
  const [newConsommation, setNewConsommation] = useState({
    idcont: '',
    consommationpre: '',
    consommationactuelle: '',
    jours: '',
    periode: new Date().toISOString().slice(0, 7) // YYYY-MM format
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [lastConsommation, setLastConsommation] = useState(null);
  const [isLoadingLastConsommation, setIsLoadingLastConsommation] = useState(false);
  const [consommationError, setConsommationError] = useState('');

  // Configuration de l'API - Serveur principal sur le port 3003
  const API_BASE_URL = 'http://localhost:3003';

  // Fonction de validation de la consommation actuelle
  const validateConsommationActuelle = (consommationActuelle) => {
    const consommationPre = parseFloat(newConsommation.consommationpre) || 0;
    const consommationAct = parseFloat(consommationActuelle);

    if (isNaN(consommationAct)) {
      setConsommationError('');
      return true; // Pas d'erreur si le champ est vide
    }

    if (consommationAct <= consommationPre) {
      setConsommationError(`❌ Erreur: La consommation actuelle (${consommationAct} m³) doit être supérieure à la consommation précédente (${consommationPre} m³)`);
      return false;
    } else {
      setConsommationError('');
      return true;
    }
  };

  useEffect(() => {
    // Charger toutes les données initiales
    loadInitialData();
  }, []);

  // Effet pour gérer le client sélectionné depuis la liste des clients
  useEffect(() => {
    if (selectedClientFromList && clients.length > 0) {
      console.log('🎯 Client sélectionné depuis la liste détecté:', selectedClientFromList);
      console.log('📋 ID du client:', selectedClientFromList.idclient);
      console.log('📋 Clients disponibles:', clients.length);

      // Pré-remplir le formulaire avec le client sélectionné
      setSelectedClient(selectedClientFromList.idclient.toString());

      // Charger automatiquement les contrats de ce client
      console.log('🔄 Appel de handleClientChange pour le client:', selectedClientFromList.idclient);
      handleClientChange(selectedClientFromList.idclient.toString());

      console.log('✅ Formulaire pré-rempli avec le client:', selectedClientFromList.nom, selectedClientFromList.prenom);
    } else if (selectedClientFromList && clients.length === 0) {
      console.log('⏳ Client sélectionné mais clients pas encore chargés - attente...');
    }
  }, [selectedClientFromList, clients]);

  // Note: La logique de filtrage des contrats est maintenant gérée par handleClientChange()
  // qui utilise l'API spécialisée /api/clients/:id/contracts

  const fetchClients = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();
      if (data.success) {
        setClients(data.data || []);
        console.log('✅ Clients chargés:', data.data.length);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
      setError('Erreur lors du chargement des clients');
    }
  };

  // Note: fetchContracts supprimée - nous utilisons maintenant l'API spécialisée
  // /api/clients/:id/contracts dans handleClientChange()

  // Fonction pour récupérer les contrats d'un client spécifique depuis la table Contract
  const handleClientChange = async (clientId) => {
    console.log('🔄 handleClientChange appelé avec clientId:', clientId);
    setSelectedClient(clientId);

    if (!clientId) {
      console.log('❌ ClientId vide - réinitialisation des contrats');
      setFilteredContracts([]);
      setNewConsommation(prev => ({
        ...prev,
        idcont: '',
        consommationpre: '',
        jours: ''
      }));
      setLastConsommation(null);
      return;
    }

    console.log(`🔍 Client ${clientId} sélectionné - Récupération des contrats depuis la table Contract...`);

    try {
        // Utiliser la nouvelle API spécifique pour récupérer les contrats du client
        const response = await fetch(`${API_BASE_URL}/api/clients/${clientId}/contracts`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            const clientContracts = data.data;
            console.log('📋 Contrats reçus de l\'API:', clientContracts);
            setFilteredContracts(clientContracts);
            console.log('✅ setFilteredContracts appelé avec:', clientContracts.length, 'contrats');

            console.log(`✅ ${clientContracts.length} contrat(s) trouvé(s) pour le client ${clientId} depuis la table Contract`);

            // Afficher les détails des contrats trouvés
            if (clientContracts.length > 0) {
              console.log('📋 Contrats disponibles:');
              clientContracts.forEach((contract, index) => {
                console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
                console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
                console.log(`      Marque compteur: ${contract.marquecompteur || 'Non définie'}`);
                console.log(`      Client: ${contract.nom} ${contract.prenom}`);
                console.log(`      Date contrat: ${new Date(contract.datecontract).toLocaleDateString()}`);
              });

              // Si le client a exactement un contrat, le sélectionner automatiquement
              if (clientContracts.length === 1) {
                const autoSelectedContract = clientContracts[0];
                console.log(`🎯 Sélection automatique du contrat unique: ${autoSelectedContract.idcontract}`);

                setNewConsommation(prev => ({
                  ...prev,
                  idcont: autoSelectedContract.idcontract.toString()
                }));

                // Récupérer automatiquement la dernière consommation de ce contrat
                fetchLastConsommation(autoSelectedContract.idcontract.toString());

                console.log(`✅ Contrat unique auto-sélectionné: ${autoSelectedContract.codeqr}`);
                return; // Sortir de la fonction pour éviter la réinitialisation
              } else if (clientContracts.length > 1) {
                console.log(`📋 ${clientContracts.length} contrats trouvés - L'utilisateur doit choisir`);
                // Réinitialiser seulement si plusieurs contrats
                setNewConsommation(prev => ({
                  ...prev,
                  idcont: '',
                  consommationpre: '',
                  jours: ''
                }));
                setLastConsommation(null);
              }
            } else {
              console.warn('⚠️ Aucun contrat trouvé pour ce client dans la table Contract');
              setFilteredContracts([]);
            }
          } else {
            setFilteredContracts([]);
            console.warn('⚠️ Erreur dans la réponse de l\'API contracts');
          }
        } else {
          console.error(`❌ Erreur HTTP ${response.status} lors de la récupération des contrats`);
          setFilteredContracts([]);
        }
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des contrats:', error);
      setFilteredContracts([]);
    }
  };

  // Fonction pour charger toutes les données initiales
  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchClients(),
        fetchLastConsommationGlobal()
      ]);
      console.log('✅ Données initiales chargées (clients + dernière consommation globale)');
    } catch (error) {
      console.error('Erreur lors du chargement initial:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour récupérer la dernière consommation d'un contrat
  const fetchLastConsommation = async (idcontract) => {
    if (!idcontract) return;

    console.log('🔍 Récupération de la dernière consommation pour le contrat:', idcontract);
    setIsLoadingLastConsommation(true);
    try {
      const url = `${API_BASE_URL}/api/contracts/${idcontract}/last-consommation`;
      console.log('📡 URL appelée:', url);

      const response = await fetch(url);
      const data = await response.json();

      console.log('📥 Réponse reçue:', data);

      if (data.success && data.data) {
        setLastConsommation(data.data);
        // Mettre à jour automatiquement la consommation précédente (dernière consommation de CE contrat)
        const newConsommationPre = data.data.consommationactuelle || '0';
        console.log('✅ Mise à jour consommationpre avec:', newConsommationPre);

        setNewConsommation(prev => ({
          ...prev,
          consommationpre: newConsommationPre,
          jours: calculateDaysBetweenPeriods(data.data.periode, prev.periode)
        }));
        console.log('✅ Dernière consommation du contrat chargée:', data.data);
      } else {
        // Pas de consommation pour ce contrat, garder la dernière consommation globale
        console.log('ℹ️ Aucune consommation trouvée pour ce contrat, conservation de la valeur globale');
        setNewConsommation(prev => ({
          ...prev,
          consommationpre: lastConsommation?.consommationactuelle || '0',
          jours: '30' // Valeur par défaut pour les jours
        }));
      }
    } catch (err) {
      console.error('Erreur lors de la récupération de la dernière consommation:', err);
      setLastConsommation(null);
    } finally {
      setIsLoadingLastConsommation(false);
    }
  };

  // Fonction pour calculer le nombre de jours entre deux périodes
  const calculateDaysBetweenPeriods = (previousPeriod, currentPeriod) => {
    if (!previousPeriod || !currentPeriod) return '30';

    try {
      // Convertir les périodes YYYY-MM en dates (premier jour du mois)
      const prevDate = new Date(previousPeriod + '-01');
      const currDate = new Date(currentPeriod + '-01');

      // Calculer la différence en millisecondes puis en jours
      const diffTime = Math.abs(currDate - prevDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      console.log(`📅 Calcul des jours: ${previousPeriod} → ${currentPeriod} = ${diffDays} jours`);
      return diffDays.toString();
    } catch (err) {
      console.error('Erreur lors du calcul des jours:', err);
      return '30';
    }
  };

  // Fonction pour récupérer la toute dernière consommation de la base de données (globale)
  const fetchLastConsommationGlobal = async () => {
    console.log('🔍 Récupération de la dernière consommation globale...');
    setIsLoadingLastConsommation(true);
    try {
      const url = `${API_BASE_URL}/api/last-consommation-global`;
      console.log('📡 URL appelée:', url);

      const response = await fetch(url);
      const data = await response.json();

      console.log('📥 Réponse globale reçue:', data);

      if (data.success && data.data) {
        setLastConsommation(data.data);
        // Afficher la dernière consommation actuelle comme consommation précédente
        const lastConsommationValue = data.data.consommationactuelle || '0';
        console.log('✅ Mise à jour consommationpre globale avec:', lastConsommationValue);

        // Et calculer automatiquement les jours avec la période actuelle
        setNewConsommation(prev => {
          const calculatedDays = calculateDaysBetweenPeriods(data.data.periode, prev.periode);
          return {
            ...prev,
            consommationpre: lastConsommationValue,
            jours: calculatedDays
          };
        });
        console.log('✅ Dernière consommation globale chargée:', data.data);
      } else {
        setLastConsommation(null);
        setNewConsommation(prev => ({
          ...prev,
          consommationpre: '0',
          jours: '30' // Valeur par défaut si aucune consommation précédente
        }));
        console.log('ℹ️ Aucune consommation trouvée dans la base de données');
      }
    } catch (err) {
      console.error('❌ Erreur lors de la récupération de la dernière consommation globale:', err);
      setLastConsommation(null);
      setNewConsommation(prev => ({
        ...prev,
        consommationpre: '0'
      }));
    } finally {
      setIsLoadingLastConsommation(false);
    }
  };

  const handleAddConsommation = async (e) => {
    e.preventDefault();

    if (!newConsommation.idcont || !newConsommation.consommationactuelle) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    // Validation de la consommation actuelle avant soumission
    if (!validateConsommationActuelle(newConsommation.consommationactuelle)) {
      alert('❌ Erreur de validation: La consommation actuelle doit être supérieure à la consommation précédente');
      return;
    }

    try {
      console.log('📝 Envoi des données:', newConsommation);

      const response = await fetch(`${API_BASE_URL}/api/consommations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newConsommation,
          idtech: user?.idtech || 1, // ID du technicien connecté
          idtranch: 1 // Tranche par défaut
        })
      });

      const data = await response.json();

      if (data.success) {
        // Trouver les informations du client pour les passer à la page de résultats
        const selectedContract = filteredContracts.find(c => c.idcontract?.toString() === newConsommation.idcont);
        const releveData = {
          ...newConsommation,
          clientNom: selectedContract ? `${selectedContract.nom} ${selectedContract.prenom}` : 'Client',
          adresse: selectedContract?.adresse || '123 Rue Allal Ben Abdellah',
          compteur: selectedContract?.codeqr || 'QR123'
        };

        // Rediriger vers la page de résultats
        if (onShowResults) {
          onShowResults(releveData);
        }
      } else {
        alert('Erreur lors de l\'ajout: ' + data.error);
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la consommation:', error);
      alert('Erreur lors de l\'ajout de la consommation');
    }
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Consommation d'Eau</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Consommation d'Eau</h1>
            <p className="tech-mobile-card-subtitle">Saisie et consultation des relevés</p>
          </div>
        </div>
      </div>

      {/* Formulaire de saisie */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Nouveau Relevé</h2>
        </div>
        <form onSubmit={handleAddConsommation} className="tech-mobile-form">
          {/* Période (YYYY-MM) - Premier champ */}
          <div className="tech-mobile-form-group">
            <label>Période (YYYY-MM) *</label>
            <input
              type="month"
              value={newConsommation.periode}
              onChange={(e) => {
                const newPeriode = e.target.value;
                // Recalculer automatiquement les jours avec la dernière période de la base de données
                if (lastConsommation && lastConsommation.periode) {
                  const calculatedDays = calculateDaysBetweenPeriods(lastConsommation.periode, newPeriode);
                  setNewConsommation(prev => ({
                    ...prev,
                    periode: newPeriode,
                    jours: calculatedDays
                  }));
                  console.log(`🔄 Période changée: ${lastConsommation.periode} → ${newPeriode} = ${calculatedDays} jours`);
                } else {
                  setNewConsommation(prev => ({
                    ...prev,
                    periode: newPeriode,
                    jours: '30' // Valeur par défaut
                  }));
                }
              }}
              className="tech-mobile-form-input"
              required
              style={{ border: '2px solid #3b82f6', backgroundColor: '#f0f8ff' }}
            />
            <small style={{ color: '#3b82f6', fontSize: '12px' }}>
              📅 Période de facturation pour ce relevé
            </small>
          </div>

          {/* Sélection du client */}
          <div className="tech-mobile-form-group">
            <label>Client *</label>
            {selectedClientFromList ? (
              // Affichage en lecture seule du client sélectionné depuis la liste
              <div>
                <input
                  type="text"
                  value={`${selectedClientFromList.nom} ${selectedClientFromList.prenom} - ${selectedClientFromList.ville || 'Ville non définie'}`}
                  className="tech-mobile-form-input"
                  readOnly
                  style={{
                    border: '2px solid #10b981',
                    backgroundColor: '#f0fdf4',
                    cursor: 'not-allowed',
                    fontWeight: 'bold',
                    color: '#059669'
                  }}
                />
                <button
                  type="button"
                  onClick={() => {
                    if (onClearSelectedClient) {
                      onClearSelectedClient();
                    }
                    setSelectedClient('');
                    setFilteredContracts([]);
                    setNewConsommation(prev => ({
                      ...prev,
                      idcont: '',
                      consommationpre: '',
                      jours: ''
                    }));
                    setLastConsommation(null);
                  }}
                  style={{
                    marginTop: '8px',
                    padding: '8px 12px',
                    backgroundColor: '#f59e0b',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  🔄 Changer de client
                </button>
              </div>
            ) : (
              // Sélection normale si aucun client n'est sélectionné depuis la liste
              <div>
                <select
                  value={selectedClient}
                  onChange={(e) => handleClientChange(e.target.value)}
                  className="tech-mobile-form-input"
                  required
                >
                  <option value="">Sélectionner un client</option>
                  {clients.map(client => (
                    <option key={client.idclient} value={client.idclient}>
                      {client.nom} {client.prenom} - {client.ville}
                    </option>
                  ))}
                </select>

                {/* Bouton pour aller à la liste des clients */}
                <button
                  type="button"
                  onClick={() => {
                    if (onShowClientList) {
                      onShowClientList();
                    }
                  }}
                  style={{
                    marginTop: '8px',
                    padding: '10px 16px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    width: '100%'
                  }}
                >
                  📋 Sélectionner depuis la liste
                </button>
              </div>
            )}

            {selectedClient && !selectedClientFromList && (
              <small style={{ color: '#059669', fontSize: '12px' }}>
                ✅ Client sélectionné - {filteredContracts.length} contrat(s) disponible(s)
              </small>
            )}
          </div>

          {/* Sélection du contrat (filtré par client) */}
          <div className="tech-mobile-form-group">
            <label>Contrat *</label>
            <select
              value={newConsommation.idcont}
              onChange={(e) => {
                const selectedContractId = e.target.value;
                setNewConsommation({...newConsommation, idcont: selectedContractId});
                // Récupérer automatiquement la dernière consommation du contrat sélectionné
                if (selectedContractId) {
                  fetchLastConsommation(selectedContractId);
                }
              }}
              className="tech-mobile-form-input"
              required
              disabled={!selectedClient || (filteredContracts.length === 1 && newConsommation.idcont)}
              style={
                filteredContracts.length === 1 && newConsommation.idcont
                  ? { border: '2px solid #10b981', backgroundColor: '#f0fdf4' } // Vert pour sélection automatique
                  : filteredContracts.length > 1
                    ? { border: '2px solid #3b82f6', backgroundColor: '#f0f8ff' } // Bleu pour sélection manuelle
                    : filteredContracts.length === 0 && selectedClient
                      ? { border: '2px solid #dc2626', backgroundColor: '#fef2f2' } // Rouge pour aucun contrat
                      : {}
              }
            >
              <option value="">
                {!selectedClient
                  ? "Sélectionnez d'abord un client"
                  : filteredContracts.length === 0
                    ? "Ce client n'a pas de contrat"
                    : filteredContracts.length === 1
                      ? "Contrat sélectionné automatiquement"
                      : "Sélectionner un contrat"
                }
              </option>
              {filteredContracts.map(contract => (
                <option key={contract.idcontract} value={contract.idcontract}>
                  {contract.codeqr || 'QR non défini'} - Contrat #{contract.idcontract}
                  {contract.marquecompteur && ` (${contract.marquecompteur})`}
                  {contract.datecontract && ` - ${new Date(contract.datecontract).toLocaleDateString()}`}
                </option>
              ))}
            </select>

            {/* Messages d'état pour les contrats */}
            {selectedClient && filteredContracts.length === 0 && (
              <small style={{ color: '#dc2626', fontSize: '12px', fontWeight: 'bold' }}>
                ❌ Ce client n'a pas de contrat - Impossible de créer une consommation
              </small>
            )}

            {filteredContracts.length === 1 && newConsommation.idcont && (
              <small style={{ color: '#10b981', fontSize: '12px', fontWeight: 'bold' }}>
                ✅ Contrat unique affiché automatiquement
              </small>
            )}

            {filteredContracts.length > 1 && !newConsommation.idcont && (
              <small style={{ color: '#3b82f6', fontSize: '12px' }}>
                📋 {filteredContracts.length} contrats disponibles - Sélectionnez-en un
              </small>
            )}

            {filteredContracts.length > 1 && newConsommation.idcont && (
              <small style={{ color: '#10b981', fontSize: '12px' }}>
                ✅ Contrat sélectionné parmi {filteredContracts.length} disponibles
              </small>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Consommation Précédente (m³)</label>
            <input
              type="number"
              step="0.1"
              placeholder="Dernière consommation de la base de données"
              value={newConsommation.consommationpre}
              className="tech-mobile-form-input"
              readOnly
              style={{ backgroundColor: '#f0f8ff', cursor: 'not-allowed', border: '2px solid #3b82f6' }}
            />
            {isLoadingLastConsommation && (
              <small style={{ color: '#3b82f6', fontSize: '12px' }}>
                📊 Chargement de la dernière consommation depuis la table Consommation...
              </small>
            )}

            {!lastConsommation && !isLoadingLastConsommation && (
              <small style={{ color: '#dc2626', fontSize: '12px' }}>
                ⚠️ Aucune consommation trouvée dans la table Consommation
              </small>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Consommation Actuelle (m³) *</label>
            <input
              type="number"
              step="0.1"
              placeholder="Saisir la nouvelle consommation"
              value={newConsommation.consommationactuelle}
              onChange={(e) => {
                const newValue = e.target.value;
                setNewConsommation({...newConsommation, consommationactuelle: newValue});

                // Validation en temps réel
                if (newValue) {
                  validateConsommationActuelle(newValue);
                } else {
                  setConsommationError('');
                }
              }}
              className="tech-mobile-form-input"
              required
              style={{
                border: consommationError ? '2px solid #dc2626' : '2px solid #10b981',
                backgroundColor: consommationError ? '#fef2f2' : '#f0fdf4'
              }}
            />
            {consommationError ? (
              <div style={{ marginTop: '8px' }}>
                <button
                  type="button"
                  style={{
                    width: '100%',
                    padding: '10px',
                    backgroundColor: '#dc2626',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '13px',
                    fontWeight: 'bold',
                    cursor: 'not-allowed',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '8px'
                  }}
                  disabled
                >
                  ❌ ERREUR DE VALIDATION
                </button>
                <small style={{
                  color: '#dc2626',
                  fontSize: '11px',
                  fontWeight: 'bold',
                  display: 'block',
                  marginTop: '4px',
                  textAlign: 'center',
                  lineHeight: '1.3'
                }}>
                  {consommationError.replace('❌ Erreur: ', '')}
                </small>
              </div>
            ) : null}
          </div>

          <div className="tech-mobile-form-group">
            <label>Nombre de jours</label>
            <input
              type="number"
              placeholder="30"
              value={newConsommation.jours}
              className="tech-mobile-form-input"
              readOnly
              style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
            />

          </div>



          <button
            type="submit"
            className="tech-mobile-action-btn complete"
            style={{
              width: 'auto',
              maxWidth: '200px',
              margin: '10px auto 0 auto',
              display: 'block',
              opacity: (selectedClient && filteredContracts.length === 0) || consommationError ? 0.5 : 1,
              cursor: (selectedClient && filteredContracts.length === 0) || consommationError ? 'not-allowed' : 'pointer'
            }}
            disabled={(selectedClient && filteredContracts.length === 0) || consommationError}
          >
            {selectedClient && filteredContracts.length === 0
              ? "❌ Client sans contrat"
              : consommationError
                ? "❌ Consommation invalide"
                : "💾 Enregistrer le relevé"
            }
          </button>

          {selectedClient && filteredContracts.length === 0 && (
            <small style={{
              color: '#dc2626',
              fontSize: '12px',
              textAlign: 'center',
              display: 'block',
              marginTop: '5px',
              fontWeight: 'bold'
            }}>
              ⚠️ Ce client doit avoir un contrat pour pouvoir enregistrer une consommation
            </small>
          )}

          {consommationError && (
            <small style={{
              color: '#dc2626',
              fontSize: '12px',
              textAlign: 'center',
              display: 'block',
              marginTop: '5px',
              fontWeight: 'bold'
            }}>
              ⚠️ Corrigez la consommation actuelle pour pouvoir enregistrer
            </small>
          )}
        </form>
      </div>
    </div>
  );
};

export default ConsommationPage;

const express = require('express');
const cors = require('cors');
const { 
  initializeDatabase, 
  getAllClients, 
  getClientContracts, 
  getLastConsommation 
} = require('./database');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Middleware de logging détaillé
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`\n📥 ${timestamp}`);
  console.log(`🔗 ${req.method} ${req.url}`);
  console.log('─'.repeat(50));
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée - Serveur fonctionnel');
  res.json({
    message: 'Serveur contrats fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 GET /api/clients - Récupération de tous les clients');
    
    const result = await getAllClients();
    
    console.log('📤 Envoi de la réponse clients');
    res.json(result);

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// 🎯 ROUTE CRITIQUE : Récupérer les contrats d'un client spécifique
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`\n🎯 ROUTE CRITIQUE: GET /api/clients/${id}/contracts`);
    
    const result = await getClientContracts(id);
    
    if (result.success) {
      console.log('📤 ENVOI DE LA RÉPONSE CONTRATS');
      res.json(result);
    } else {
      console.log('❌ Client non trouvé');
      res.status(404).json(result);
    }

  } catch (error) {
    console.error('❌ ERREUR CRITIQUE lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message,
      client_id: parseInt(req.params.id)
    });
  }
});

// Route pour récupérer la dernière consommation
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 GET /api/contracts/${id}/last-consommation`);

    const result = await getLastConsommation(id);
    res.json(result);

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route de connexion (pour l'authentification)
app.post('/login', async (req, res) => {
  console.log('Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // Pour le test, accepter <EMAIL> / Tech123
    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      console.log('✅ Connexion réussie pour:', email);
      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          idtech: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: email,
          role: 'Tech'
        },
        redirectTo: '/technician-dashboard'
      });
    } else {
      console.log('❌ Identifiants incorrects pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

  } catch (err) {
    console.error('Erreur lors de la connexion:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: err.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur non gérée:', err.message);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
const PORT = 3002;

async function startServer() {
  try {
    console.log('🚀 Démarrage du serveur contrats...');
    
    // Initialiser la base de données
    await initializeDatabase();
    console.log('✅ Base de données initialisée');

    app.listen(PORT, () => {
      console.log(`\n🚀 SERVEUR CONTRATS DÉMARRÉ sur http://localhost:${PORT}`);
      console.log('📊 Base de données: Facutration');
      console.log('📡 Routes disponibles:');
      console.log('  - GET  / (test)');
      console.log('  - POST /login (authentification)');
      console.log('  - GET  /api/clients (tous les clients)');
      console.log('  - GET  /api/clients/:id/contracts (contrats du client) ⭐ CRITIQUE');
      console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
      console.log('\n✅ PRÊT À RECEVOIR LES REQUÊTES !');
      console.log('🔍 TOUS LES APPELS SERONT LOGGÉS EN DÉTAIL');
      console.log('═'.repeat(60));
    });

  } catch (error) {
    console.error('❌ Erreur lors du démarrage du serveur:', error.message);
    process.exit(1);
  }
}

startServer();

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err.message);
  console.error('❌ Stack:', err.stack);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err.message);
  console.error('❌ Stack:', err.stack);
});
